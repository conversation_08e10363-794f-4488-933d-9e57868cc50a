# Unified WMS Lambda - Complete Documentation

## Overview

The Unified WMS Lambda replaces both Redis and Typesense lambdas for inventory processing, providing optimized performance by reading previous inventory data from Redis instead of Typesense.

## Architecture

### Previous Architecture (Inefficient)
```
MSK Topic → Redis Lambda → Redis Storage
         → Typesense Lambda → Read from Typesense → Update Typesense
```

### New Unified Architecture (Optimized)
```
MSK Topic → Unified Lambda → Read from Redis → Update Typesense → Update Redis
```

## Business Logic

### Quantity Calculation Rules

The system uses two configurable parameters:
- **`SAFETY_QUANTITY`** (default: 10) - Reserved stock not shown to customers
- **`MAX_DISPLAY_QUANTITY`** (default: 20) - Maximum quantity displayed to prevent overselling

#### Formulas:
- **Availability**: `is_available = wms_quantity > SAFETY_QUANTITY`
- **Display Quantity**: `typesense_qty = min(wms_quantity - SAFETY_QUANTITY, MAX_DISPLAY_QUANTITY)`

### Update Trigger Logic

Typesense is updated when **either**:
- `is_available` status changes (availability flip)
- `typesense_qty` changes (within displayable range)

No updates when both values remain the same.

## Detailed Scenarios

### Decreasing Quantity Scenarios

| WMS Qty | Typesense Qty | Available | Action | Reason |
|---------|---------------|-----------|---------|---------|
| 25 → 24 | 15 → 14 | true → true | ✅ Update | Quantity decreased |
| 20 → 19 | 10 → 9 | true → true | ✅ Update | Quantity decreased |
| 15 → 14 | 5 → 4 | true → true | ✅ Update | Quantity decreased |
| 12 → 11 | 2 → 1 | true → true | ✅ Update | Quantity decreased |
| **11 → 10** | **1 → 0** | **true → false** | ✅ **Update (Flip)** | **Availability flipped** |
| 10 → 9 | 0 → 0 | false → false | ❌ No update | Both values same |
| 9 → 5 | 0 → 0 | false → false | ❌ No update | Both values same |

### Increasing Quantity Scenarios

| WMS Qty | Typesense Qty | Available | Action | Reason |
|---------|---------------|-----------|---------|---------|
| 5 → 9 | 0 → 0 | false → false | ❌ No update | Both values same |
| 9 → 10 | 0 → 0 | false → false | ❌ No update | Both values same |
| **10 → 11** | **0 → 1** | **false → true** | ✅ **Update (Flip)** | **Availability flipped** |
| 11 → 12 | 1 → 2 | true → true | ✅ Update | Quantity increased |
| 15 → 20 | 5 → 10 | true → true | ✅ Update | Quantity increased |
| 25 → 30 | 15 → 20 | true → true | ✅ Update | Quantity increased |
| 30 → 35 | 20 → 20 | true → true | ❌ No update | Both values same (capped) |
| 35 → 100 | 20 → 20 | true → true | ❌ No update | Both values same (capped) |

### Edge Cases

| WMS Qty | Typesense Qty | Available | Action | Reason |
|---------|---------------|-----------|---------|---------|
| 0 → 0 | 0 → 0 | false → false | ❌ No update | Both values same |
| 30 → 31 | 20 → 20 | true → true | ❌ No update | Capped at MAX_DISPLAY |
| 50 → 100 | 20 → 20 | true → true | ❌ No update | Capped at MAX_DISPLAY |

## Configuration

### Environment Variables

#### Redis Configuration
- `REDIS_URL`: Redis connection URL
- `LOG_LEVEL`: Logging level (default: INFO)
- `PAYLOAD_DECODE`: Whether to decode base64 payloads (default: true)

#### Typesense Configuration
- `TYPESENSE_URL`: Typesense API URL
- `TYPESENSE_API_KEY`: Typesense API key
- `TYPESENSE_COLLECTION`: Typesense collection name

#### Business Logic Configuration
- `SAFETY_QUANTITY`: Safety/buffer stock quantity (default: 10)
- `MAX_DISPLAY_QUANTITY`: Maximum quantity to display to customers (default: 20)

### Configuration Examples

#### Conservative Approach (Higher Safety Stock)
```bash
SAFETY_QUANTITY=15
MAX_DISPLAY_QUANTITY=25
```

#### Aggressive Approach (Lower Safety Stock)
```bash
SAFETY_QUANTITY=5
MAX_DISPLAY_QUANTITY=15
```

#### High-Volume Products
```bash
SAFETY_QUANTITY=20
MAX_DISPLAY_QUANTITY=50
```

## Technical Implementation

### Key Components

1. **`UnifiedStockMessageProcessor`**: Core business logic
2. **`RedisJSONWrapper`**: Redis operations for caching
3. **`TypesenseWrapper`**: Typesense operations with bulk updates
4. **`lambda_function.py`**: AWS Lambda entry point

### Redis Key Structure

- **Stock Data**: `stock:{warehouse}:{sku}`
- **Timestamp**: `stock_ts:{warehouse}:{sku}`

### Performance Optimizations

1. **Redis Reads**: Previous quantity from Redis (not Typesense)
2. **Bulk Updates**: Single API call for multiple documents
3. **Smart Triggers**: Only update when values actually change
4. **Timestamp Validation**: Skip older/duplicate messages

## Monitoring and Logging

### Log Prefixes
All logs use `UNIFIED_PROCESSOR:` prefix for easy filtering.

### Key Log Messages

#### Successful Processing
```
UNIFIED_PROCESSOR: Values changing for WH1:SKU123 - Availability: false→true, Qty: 0→5
UNIFIED_PROCESSOR: Bulk updating 3 documents for WH1:SKU123
UNIFIED_PROCESSOR: Bulk update completed for WH1:SKU123 - Success: 3/3
```

#### No Update Needed
```
UNIFIED_PROCESSOR: No Typesense update needed - Availability: true→true, Qty: 20→20 (no changes)
```

#### Error Scenarios
```
UNIFIED_PROCESSOR: DOCUMENT_NOT_FOUND: No documents found for store_id:WH1, sku_code:SKU123
UNIFIED_PROCESSOR: Error in bulk update for WH1:SKU123: Connection timeout
```

## Deployment

### Docker Build
```bash
docker build -t unified-wms-lambda .
```

### Local Testing
```bash
docker-compose up
```

### AWS Lambda Deployment
Use your existing deployment pipeline with the new environment variables.

## Rollback Plan

If issues arise:
1. Redeploy original separate Redis and Typesense lambdas
2. Update MSK topic consumers to point back to separate lambdas
3. Redis data structure remains compatible for seamless rollback

## Benefits

- **🚀 Performance**: Redis reads ~10x faster than Typesense
- **📈 Scalability**: Handles higher message volumes
- **⚙️ Flexibility**: Configurable business rules
- **🔧 Efficiency**: Bulk updates reduce API calls
- **📊 Monitoring**: Enhanced logging and error tracking
- **🔄 Reliability**: Comprehensive error handling and fallbacks
