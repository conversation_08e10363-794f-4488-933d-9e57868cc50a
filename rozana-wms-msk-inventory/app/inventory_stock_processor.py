from dateutil.parser import parse
from datetime import datetime
import json
import logging
import os
from app.redis_wrapper import RedisJSONWrapper
from app.typesense_wrapper import TypesenseWrapper
from urllib.parse import quote_plus, unquote_plus

logger = logging.getLogger(__name__)

# Environment variables for quantity configuration
SAFETY_QUANTITY = int(os.environ.get('SAFETY_QUANTITY', '10'))
MAX_DISPLAY_QUANTITY = int(os.environ.get('MAX_DISPLAY_QUANTITY', '20'))

class InventoryStockMessageProcessor:
    def __init__(self, redis_wrapper: RedisJSONWrapper, typesense_wrapper: TypesenseWrapper):
        self.redis = redis_wrapper
        self.typesense = typesense_wrapper

    @staticmethod
    def _safe(part: str) -> str:
        """Encode dynamic key segments so Redis keys contain only URL-safe chars."""
        return quote_plus(str(part), safe='')

    def _stock_key(self, warehouse_name, sku_code: str | None = None):
        """Return the Redis key for a given warehouse / SKU."""
        wh_enc = self._safe(warehouse_name)
        if sku_code:
            sku_enc = self._safe(sku_code)
            return f"stock:{wh_enc}:{sku_enc}"
        return f"stock:{wh_enc}:*"

    def _timestamp_key(self, warehouse_name, sku_code: str | None = None):
        """Return the Redis timestamp key for a given warehouse / SKU."""
        wh_enc = self._safe(warehouse_name)
        if sku_code:
            sku_enc = self._safe(sku_code)
            return f"stock_ts:{wh_enc}:{sku_enc}"
        return f"stock_ts:{wh_enc}:*"

    def process(self, warehouse_name, sku_code, payload, timestamp_str, idempotent_key=None, force_typesense_update=False):
        """
        Unified processing: Read from Redis, update Typesense if needed, then update Redis.
        
        Args:
            warehouse_name: Name of the warehouse
            sku_code: SKU code of the product
            payload: The stock payload data
            timestamp_str: Timestamp of the message
            idempotent_key: Optional idempotent key for deduplication
            force_typesense_update: If True, always update Typesense regardless of validation
        Returns:
            bool: True if processing was successful, False otherwise
        """
        logger.info(f"INVENTORY_PROCESSOR: Processing stock message for {warehouse_name}:{sku_code}")

        # Step 1: Validate timestamp (unless forceful sync is requested)
        redis_ts_key = self._timestamp_key(warehouse_name, sku_code)
        incoming_ts = parse(timestamp_str)
        last_ts_str = self.redis.redis_client.get(redis_ts_key)
        if last_ts_str:
            last_ts = parse(last_ts_str.decode() if isinstance(last_ts_str, bytes) else last_ts_str)
            if incoming_ts <= last_ts and not force_typesense_update:
                logger.info(f"INVENTORY_PROCESSOR: Skipping older/duplicate message for {warehouse_name}:{sku_code}")
                return False  # older or duplicate message, skip

        # Step 2: Extract available quantity from payload
        available_quantity = self._extract_available_quantity(payload)
        if available_quantity is None:
            logger.error(f"INVENTORY_PROCESSOR: No available_quantity found in payload: {payload}")
            return False

        logger.info(f"INVENTORY_PROCESSOR: New available quantity: {available_quantity}")
        wms_available_quantity = int(available_quantity)

        # Step 3: Get previous available quantity from Redis (instead of Typesense)
        previous_available_quantity = self._get_previous_available_quantity(warehouse_name, sku_code)
        logger.info(f"INVENTORY_PROCESSOR: Previous available quantity from Redis: {previous_available_quantity}")

        # Step 4: Apply Typesense update logic
        typesense_updated = self._update_typesense_if_needed(
            warehouse_name, sku_code, wms_available_quantity, previous_available_quantity,
            force_update=force_typesense_update
        )

        # Step 5: Update Redis with new data (same as Redis lambda)
        redis_updated = self._update_redis(warehouse_name, sku_code, payload, timestamp_str)

        if typesense_updated:
            logger.info(f"INVENTORY_PROCESSOR: Successfully updated both Typesense and Redis for {warehouse_name}:{sku_code}")
        elif redis_updated:
            logger.info(f"INVENTORY_PROCESSOR: Updated Redis only for {warehouse_name}:{sku_code} (no Typesense changes needed)")
        else:
            logger.error(f"INVENTORY_PROCESSOR: Failed to update Redis for {warehouse_name}:{sku_code}")
            return False

        return True

    def _extract_available_quantity(self, payload):
        """Extract available quantity from payload (same logic as original)"""
        if 'data' in payload and 'available_quantity' in payload['data']:
            return payload['data']['available_quantity']
        elif 'available_quantity' in payload:
            return payload['available_quantity']
        return None

    def _get_previous_available_quantity(self, warehouse_name, sku_code):
        """Get previous available quantity from Redis instead of Typesense"""
        redis_stock_key = self._stock_key(warehouse_name, sku_code)
        stock_data = self.redis.redis_client.get(redis_stock_key)
        
        if stock_data:
            try:
                previous_payload = json.loads(stock_data)
                previous_qty = self._extract_available_quantity(previous_payload)
                return int(previous_qty) if previous_qty is not None else 0
            except Exception as e:
                logger.error(f"INVENTORY_PROCESSOR: Error parsing previous Redis data: {e}")
                return 0
        
        # No previous data in Redis - treat as first update
        logger.info(f"INVENTORY_PROCESSOR: No previous data in Redis for {warehouse_name}:{sku_code}, treating as first update")
        return 0

    def _calculate_typesense_values(self, wms_qty):
        """Calculate is_available and typesense_qty based on configurable business rules"""
        is_available = wms_qty > SAFETY_QUANTITY
        if wms_qty <= SAFETY_QUANTITY:
            typesense_qty = 0
        else:
            typesense_qty = min(wms_qty - SAFETY_QUANTITY, MAX_DISPLAY_QUANTITY)
        return is_available, typesense_qty

    def _update_typesense_if_needed(self, warehouse_name, sku_code, wms_available_quantity, previous_available_quantity, force_update=False):
        """
    Update Typesense using new quantity logic with safety stock and display cap.

    Args:
        warehouse_name: Name of the warehouse
        sku_code: SKU code of the product
        wms_available_quantity: New available quantity from WMS
        previous_available_quantity: Previous available quantity from Redis
        force_update: If True, always update Typesense regardless of business logic changes

    Returns:
        bool: True if Typesense was updated, False otherwise
    """
        facility_code = warehouse_name

        # Calculate previous and new typesense values
        previous_availability, previous_typesense_qty = self._calculate_typesense_values(previous_available_quantity)
        new_availability, new_typesense_qty = self._calculate_typesense_values(wms_available_quantity)

        logger.info(f"INVENTORY_PROCESSOR: Previous availability: {previous_availability}, previous typesense quantity: {previous_typesense_qty}")
        logger.info(f"INVENTORY_PROCESSOR: New availability: {new_availability}, new typesense quantity: {new_typesense_qty}")

        # Update if either availability status OR typesense quantity changes
        if force_update or new_availability != previous_availability or new_typesense_qty != previous_typesense_qty:
            logger.info(f"INVENTORY_PROCESSOR: Values changing for {facility_code}:{sku_code} - Availability: {previous_availability}→{new_availability}, Qty: {previous_typesense_qty}→{new_typesense_qty}")
            
            # Find the document in Typesense (collection is automatically determined by SKU)
            search_result = self.typesense.find_by_facility_code_and_sku(facility_code, sku_code)

            if 'error' in search_result:
                logger.error(f"INVENTORY_PROCESSOR: Error finding document in Typesense: {search_result['error']}")
                return False

            # Check if we found any hits
            if 'hits' not in search_result or len(search_result['hits']) == 0:
                logger.error(f"INVENTORY_PROCESSOR: DOCUMENT_NOT_FOUND: No documents found for store_id:{facility_code}, sku_code:{sku_code} - ACTION_REQUIRED")
                return False

            # Log info about multiple documents found (this is now expected)
            num_documents = len(search_result['hits'])
            logger.info(f"INVENTORY_PROCESSOR: Found {num_documents} documents for store_id:{facility_code}, sku_code:{sku_code} - updating all")

            # Determine target collection for bulk update (same logic as search)
            target_collection = self.typesense._get_collection_for_sku(sku_code)

            # Prepare bulk update data for all matching documents
            bulk_update_data = []
            document_ids = []
            
            for hit in search_result['hits']:
                document_id = hit['document']['id']
                document_ids.append(document_id)
                bulk_update_data.append({
                    'id': document_id,
                    'is_available': new_availability,
                    'available_qty': new_typesense_qty
                })

            logger.info(f"INVENTORY_PROCESSOR: Bulk updating {len(bulk_update_data)} documents in collection '{target_collection}' for {facility_code}:{sku_code}")
            logger.info(f"INVENTORY_PROCESSOR: Document IDs: {document_ids}")
            
            # Perform bulk update with target collection
            bulk_result = self.typesense.bulk_update_documents(bulk_update_data, collection=target_collection)
            
            if 'error' in bulk_result:
                logger.error(f"INVENTORY_PROCESSOR: Error in bulk update for {facility_code}:{sku_code}: {bulk_result['error']}")
                return False
            
            success_count = bulk_result.get('success_count', 0)
            total_count = len(bulk_update_data)
            
            logger.info(f"INVENTORY_PROCESSOR: Bulk update completed for {facility_code}:{sku_code} - Success: {success_count}/{total_count}")
            logger.info(f"INVENTORY_PROCESSOR: Updated documents to is_available={new_availability}, available_qty={new_typesense_qty}")
            
            # Return True if at least one document was updated successfully
            return success_count > 0
        else:
            logger.info(f"INVENTORY_PROCESSOR: No Typesense update needed - Availability: {previous_availability}→{new_availability}, Qty: {previous_typesense_qty}→{new_typesense_qty} (no changes)")
            return False

    def _update_redis(self, warehouse_name, sku_code, payload, timestamp_str):
        """Update Redis with new data (same logic as Redis lambda)"""
        try:
            redis_stock_key = self._stock_key(warehouse_name, sku_code)
            redis_ts_key = self._timestamp_key(warehouse_name, sku_code)

            # Update both data and timestamp
            self.redis.redis_client.set(redis_stock_key, json.dumps(payload))
            self.redis.redis_client.set(redis_ts_key, timestamp_str)
            
            logger.info(f"INVENTORY_PROCESSOR: Successfully updated Redis for {warehouse_name}:{sku_code}")
            return True
        except Exception as e:
            logger.error(f"INVENTORY_PROCESSOR: Error updating Redis: {e}")
            return False

    def get_stock(self, warehouse_name, sku_code):
        """Get stock data from Redis"""
        redis_stock_key = self._stock_key(warehouse_name, sku_code)
        stock_data = self.redis.redis_client.get(redis_stock_key)
        if stock_data:
            return json.loads(stock_data)
        return None

    def get_all_stock(self, warehouse_name):
        """Get all stock data for a warehouse from Redis"""
        pattern = self._stock_key(warehouse_name)  # wildcard pattern
        keys = self.redis.keys(pattern)
        result = {}
        for k in keys:
            raw = self.redis.redis_client.get(k)
            if raw:
                try:
                    sku_code = unquote_plus(k.split(":")[-1])
                    result[sku_code] = json.loads(raw)
                except Exception:
                    continue
        return result
    